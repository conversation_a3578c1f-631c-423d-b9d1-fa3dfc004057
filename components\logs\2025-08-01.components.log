2025-08-01 08:41:04.876 [info] trigger 学习库/Deep learning/pytorch/LinearModel.py resolve  [object Object] 
2025-08-01 08:41:04.877 [info] index finished after resolve  [object Object] 
2025-08-01 08:41:04.878 [info] refresh page data from modify listeners 0 1019   
2025-08-01 08:41:04.892 [info] indexing created file components/logs/2025-08-01.components.log  [object Object] 
2025-08-01 08:41:04.893 [info] refresh page data from created listeners 0 1020   
2025-08-01 08:41:14.549 [info] trigger 学习库/Deep learning/pytorch/LinearModel.py resolve  [object Object] 
2025-08-01 08:41:14.549 [info] index finished after resolve  [object Object] 
2025-08-01 08:41:14.550 [info] refresh page data from modify listeners 0 1020   
2025-08-01 08:41:28.794 [info] trigger 学习库/Deep learning/pytorch/LinearModel.py resolve  [object Object] 
2025-08-01 08:41:28.795 [info] index finished after resolve  [object Object] 
2025-08-01 08:41:28.796 [info] refresh page data from modify listeners 0 1020   
2025-08-01 08:43:15.563 [info] trigger 学习库/Deep learning/pytorch/LinearModel.py resolve  [object Object] 
2025-08-01 08:43:15.564 [info] index finished after resolve  [object Object] 
2025-08-01 08:43:15.566 [info] refresh page data from modify listeners 0 1020   
2025-08-01 08:44:26.979 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:44:26.986 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:44:26.988 [info] index finished after resolve  [object Object] 
2025-08-01 08:44:26.988 [info] refresh page data from resolve listeners 0 1020   
2025-08-01 08:44:29.101 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:44:29.193 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:44:29.196 [info] index finished after resolve  [object Object] 
2025-08-01 08:44:29.197 [info] refresh page data from resolve listeners 0 1020   
2025-08-01 08:44:31.165 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:44:31.217 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:44:31.221 [info] index finished after resolve  [object Object] 
2025-08-01 08:44:31.222 [info] refresh page data from resolve listeners 0 1020   
2025-08-01 08:44:33.259 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:44:33.268 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:44:33.269 [info] index finished after resolve  [object Object] 
2025-08-01 08:44:33.270 [info] refresh page data from resolve listeners 0 1020   
2025-08-01 08:44:43.898 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:44:43.903 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:44:43.904 [info] index finished after resolve  [object Object] 
2025-08-01 08:44:43.904 [info] refresh page data from resolve listeners 0 1020   
2025-08-01 08:44:46.839 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:44:46.844 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:44:46.846 [info] index finished after resolve  [object Object] 
2025-08-01 08:44:46.846 [info] refresh page data from resolve listeners 0 1020   
2025-08-01 08:44:49.943 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:44:49.949 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:44:49.952 [info] index finished after resolve  [object Object] 
2025-08-01 08:44:49.952 [info] refresh page data from resolve listeners 0 1020   
2025-08-01 08:44:52.087 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:44:52.092 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:44:52.094 [info] index finished after resolve  [object Object] 
2025-08-01 08:44:52.094 [info] refresh page data from resolve listeners 0 1020   
2025-08-01 08:44:54.335 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:44:54.339 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:44:54.341 [info] index finished after resolve  [object Object] 
2025-08-01 08:44:54.342 [info] refresh page data from resolve listeners 0 1020   
2025-08-01 08:44:56.618 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:44:56.624 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:44:56.625 [info] index finished after resolve  [object Object] 
2025-08-01 08:44:56.625 [info] refresh page data from resolve listeners 0 1020   
2025-08-01 08:44:58.677 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:44:58.684 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:44:58.685 [info] index finished after resolve  [object Object] 
2025-08-01 08:44:58.685 [info] refresh page data from resolve listeners 0 1020   
2025-08-01 08:45:00.959 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:45:00.966 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:45:00.967 [info] index finished after resolve  [object Object] 
2025-08-01 08:45:00.968 [info] refresh page data from resolve listeners 0 1020   
2025-08-01 08:45:03.159 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:45:03.166 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:45:03.168 [info] index finished after resolve  [object Object] 
2025-08-01 08:45:03.169 [info] refresh page data from resolve listeners 0 1020   
2025-08-01 08:45:05.257 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:45:05.263 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:45:05.265 [info] index finished after resolve  [object Object] 
2025-08-01 08:45:05.265 [info] refresh page data from resolve listeners 0 1020   
2025-08-01 08:45:13.141 [info] indexing created file 学习库/Deep learning/pytorch/attachments/1. 线性模型（Linear Model）-2025-08-01-08-45-13.png  [object Object] 
2025-08-01 08:45:13.197 [info] refresh page data from created listeners 0 1021   
2025-08-01 08:45:15.209 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:45:15.215 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:45:15.217 [info] index finished after resolve  [object Object] 
2025-08-01 08:45:15.217 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:46:51.640 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:46:51.644 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:46:51.646 [info] index finished after resolve  [object Object] 
2025-08-01 08:46:51.646 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:46:53.633 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:46:53.638 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:46:53.640 [info] index finished after resolve  [object Object] 
2025-08-01 08:46:53.641 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:46:57.976 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:46:57.983 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:46:57.985 [info] index finished after resolve  [object Object] 
2025-08-01 08:46:57.985 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:47:02.241 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:47:02.246 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:47:02.247 [info] index finished after resolve  [object Object] 
2025-08-01 08:47:02.248 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:47:04.344 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:47:04.350 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:47:04.351 [info] index finished after resolve  [object Object] 
2025-08-01 08:47:04.352 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:47:07.191 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:47:07.197 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:47:07.199 [info] index finished after resolve  [object Object] 
2025-08-01 08:47:07.200 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:47:11.068 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:47:11.071 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:47:11.073 [info] index finished after resolve  [object Object] 
2025-08-01 08:47:11.074 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:47:13.424 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:47:13.428 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:47:13.433 [info] index finished after resolve  [object Object] 
2025-08-01 08:47:13.434 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:47:15.449 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:47:15.454 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:47:15.456 [info] index finished after resolve  [object Object] 
2025-08-01 08:47:15.456 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:47:27.749 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:47:27.754 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:47:27.756 [info] index finished after resolve  [object Object] 
2025-08-01 08:47:27.756 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:47:29.811 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:47:29.818 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:47:29.819 [info] index finished after resolve  [object Object] 
2025-08-01 08:47:29.820 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:47:32.151 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:47:32.156 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:47:32.159 [info] index finished after resolve  [object Object] 
2025-08-01 08:47:32.159 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:47:34.173 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:47:34.178 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:47:34.180 [info] index finished after resolve  [object Object] 
2025-08-01 08:47:34.181 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:47:36.242 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:47:36.249 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:47:36.250 [info] index finished after resolve  [object Object] 
2025-08-01 08:47:36.250 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:47:38.357 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:47:38.362 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:47:38.365 [info] index finished after resolve  [object Object] 
2025-08-01 08:47:38.366 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:47:40.647 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:47:40.654 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:47:40.656 [info] index finished after resolve  [object Object] 
2025-08-01 08:47:40.657 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:47:43.029 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:47:43.036 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:47:43.038 [info] index finished after resolve  [object Object] 
2025-08-01 08:47:43.038 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:47:45.224 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:47:45.229 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:47:45.232 [info] index finished after resolve  [object Object] 
2025-08-01 08:47:45.233 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:47:47.285 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:47:47.290 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:47:47.291 [info] index finished after resolve  [object Object] 
2025-08-01 08:47:47.291 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:47:49.433 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:47:49.438 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:47:49.439 [info] index finished after resolve  [object Object] 
2025-08-01 08:47:49.440 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:47:51.847 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:47:51.854 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:47:51.855 [info] index finished after resolve  [object Object] 
2025-08-01 08:47:51.856 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:47:53.842 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:47:53.850 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:47:53.851 [info] index finished after resolve  [object Object] 
2025-08-01 08:47:53.851 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:47:55.942 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:47:55.948 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:47:55.949 [info] index finished after resolve  [object Object] 
2025-08-01 08:47:55.950 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:47:59.013 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:47:59.019 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:47:59.020 [info] index finished after resolve  [object Object] 
2025-08-01 08:47:59.021 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:48:02.423 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:48:02.427 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:48:02.430 [info] index finished after resolve  [object Object] 
2025-08-01 08:48:02.430 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:48:04.510 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:48:04.517 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:48:04.518 [info] index finished after resolve  [object Object] 
2025-08-01 08:48:04.518 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:48:06.827 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:48:06.835 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:48:06.836 [info] index finished after resolve  [object Object] 
2025-08-01 08:48:06.837 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:48:09.067 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:48:09.072 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:48:09.073 [info] index finished after resolve  [object Object] 
2025-08-01 08:48:09.074 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:48:11.119 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:48:11.123 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:48:11.125 [info] index finished after resolve  [object Object] 
2025-08-01 08:48:11.125 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:48:13.675 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:48:13.682 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:48:13.683 [info] index finished after resolve  [object Object] 
2025-08-01 08:48:13.684 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:48:19.436 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:48:19.442 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:48:19.443 [info] index finished after resolve  [object Object] 
2025-08-01 08:48:19.444 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:48:21.779 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:48:21.786 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:48:21.787 [info] index finished after resolve  [object Object] 
2025-08-01 08:48:21.788 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:48:24.159 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:48:24.164 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:48:24.165 [info] index finished after resolve  [object Object] 
2025-08-01 08:48:24.166 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:48:26.334 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:48:26.341 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:48:26.342 [info] index finished after resolve  [object Object] 
2025-08-01 08:48:26.342 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:48:28.553 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:48:28.559 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:48:28.560 [info] index finished after resolve  [object Object] 
2025-08-01 08:48:28.561 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:48:30.794 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:48:30.799 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:48:30.801 [info] index finished after resolve  [object Object] 
2025-08-01 08:48:30.801 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:48:33.469 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:48:33.474 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:48:33.475 [info] index finished after resolve  [object Object] 
2025-08-01 08:48:33.476 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:48:35.499 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:48:35.504 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:48:35.507 [info] index finished after resolve  [object Object] 
2025-08-01 08:48:35.507 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:48:37.774 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:48:37.779 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:48:37.780 [info] index finished after resolve  [object Object] 
2025-08-01 08:48:37.781 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:48:42.424 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:48:42.431 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:48:42.433 [info] index finished after resolve  [object Object] 
2025-08-01 08:48:42.434 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:48:44.874 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:48:44.879 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:48:44.881 [info] index finished after resolve  [object Object] 
2025-08-01 08:48:44.881 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:48:47.080 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:48:47.085 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:48:47.086 [info] index finished after resolve  [object Object] 
2025-08-01 08:48:47.087 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:48:49.343 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:48:49.350 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:48:49.351 [info] index finished after resolve  [object Object] 
2025-08-01 08:48:49.352 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:48:55.543 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:48:55.548 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:48:55.552 [info] index finished after resolve  [object Object] 
2025-08-01 08:48:55.552 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:48:58.122 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:48:58.128 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:48:58.129 [info] index finished after resolve  [object Object] 
2025-08-01 08:48:58.130 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:49:00.246 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:49:00.253 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:49:00.254 [info] index finished after resolve  [object Object] 
2025-08-01 08:49:00.254 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:49:02.468 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:49:02.475 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:49:02.477 [info] index finished after resolve  [object Object] 
2025-08-01 08:49:02.477 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:49:04.504 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:49:04.509 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:49:04.511 [info] index finished after resolve  [object Object] 
2025-08-01 08:49:04.512 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:49:06.743 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:49:06.748 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:49:06.751 [info] index finished after resolve  [object Object] 
2025-08-01 08:49:06.751 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:49:08.851 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:49:08.856 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:49:08.858 [info] index finished after resolve  [object Object] 
2025-08-01 08:49:08.858 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:49:11.091 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:49:11.098 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:49:11.101 [info] index finished after resolve  [object Object] 
2025-08-01 08:49:11.101 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:49:13.151 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:49:13.156 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:49:13.157 [info] index finished after resolve  [object Object] 
2025-08-01 08:49:13.158 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:49:16.915 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:49:16.921 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:49:16.923 [info] index finished after resolve  [object Object] 
2025-08-01 08:49:16.923 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:49:22.905 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:49:22.911 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:49:22.912 [info] index finished after resolve  [object Object] 
2025-08-01 08:49:22.912 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:49:29.260 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:49:29.270 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:49:29.272 [info] index finished after resolve  [object Object] 
2025-08-01 08:49:29.273 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:49:31.504 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 08:49:31.510 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 08:49:31.512 [info] index finished after resolve  [object Object] 
2025-08-01 08:49:31.512 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:50:05.554 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 08:50:05.562 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 08:50:05.563 [info] index finished after resolve  [object Object] 
2025-08-01 08:50:05.563 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:50:08.029 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 08:50:08.179 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 08:50:08.180 [info] index finished after resolve  [object Object] 
2025-08-01 08:50:08.181 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:50:10.130 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 08:50:10.306 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 08:50:10.310 [info] index finished after resolve  [object Object] 
2025-08-01 08:50:10.311 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:50:12.257 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 08:50:12.265 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 08:50:12.385 [info] index finished after resolve  [object Object] 
2025-08-01 08:50:12.386 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:50:14.370 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 08:50:14.377 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 08:50:14.526 [info] index finished after resolve  [object Object] 
2025-08-01 08:50:14.527 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:50:16.524 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 08:50:16.530 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 08:50:16.532 [info] index finished after resolve  [object Object] 
2025-08-01 08:50:16.532 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:50:18.630 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 08:50:18.637 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 08:50:18.782 [info] index finished after resolve  [object Object] 
2025-08-01 08:50:18.782 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:50:20.737 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 08:50:20.886 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 08:50:20.888 [info] index finished after resolve  [object Object] 
2025-08-01 08:50:20.888 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:50:25.241 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 08:50:25.246 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 08:50:25.247 [info] index finished after resolve  [object Object] 
2025-08-01 08:50:25.247 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:50:34.973 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 08:50:34.978 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 08:50:34.979 [info] index finished after resolve  [object Object] 
2025-08-01 08:50:34.980 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:51:01.275 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 08:51:01.361 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 08:51:01.364 [info] index finished after resolve  [object Object] 
2025-08-01 08:51:01.365 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:51:21.802 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 08:51:21.845 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 08:51:21.846 [info] index finished after resolve  [object Object] 
2025-08-01 08:51:21.847 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:57:09.939 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md   
2025-08-01 08:57:09.948 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md resolve  [object Object] 
2025-08-01 08:57:09.952 [info] index finished after resolve  [object Object] 
2025-08-01 08:57:09.953 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:57:29.433 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md   
2025-08-01 08:57:29.440 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md resolve  [object Object] 
2025-08-01 08:57:29.446 [info] index finished after resolve  [object Object] 
2025-08-01 08:57:29.447 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:57:57.447 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md   
2025-08-01 08:57:57.454 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md resolve  [object Object] 
2025-08-01 08:57:57.459 [info] index finished after resolve  [object Object] 
2025-08-01 08:57:57.460 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:58:08.075 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md   
2025-08-01 08:58:08.085 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md resolve  [object Object] 
2025-08-01 08:58:08.090 [info] index finished after resolve  [object Object] 
2025-08-01 08:58:08.091 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:59:21.965 [debug] ignore file modify evnet 学习库/Deep learning/概念库/评价指标.md   
2025-08-01 08:59:21.972 [info] trigger 学习库/Deep learning/概念库/评价指标.md resolve  [object Object] 
2025-08-01 08:59:21.974 [info] index finished after resolve  [object Object] 
2025-08-01 08:59:21.975 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:59:24.054 [debug] ignore file modify evnet 学习库/Deep learning/概念库/评价指标.md   
2025-08-01 08:59:24.061 [info] trigger 学习库/Deep learning/概念库/评价指标.md resolve  [object Object] 
2025-08-01 08:59:24.063 [info] index finished after resolve  [object Object] 
2025-08-01 08:59:24.064 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 08:59:29.984 [debug] ignore file modify evnet 学习库/Deep learning/概念库/评价指标.md   
2025-08-01 08:59:29.989 [info] trigger 学习库/Deep learning/概念库/评价指标.md resolve  [object Object] 
2025-08-01 08:59:29.990 [info] index finished after resolve  [object Object] 
2025-08-01 08:59:29.991 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:00:02.757 [debug] ignore file modify evnet 学习库/Deep learning/概念库/评价指标.md   
2025-08-01 09:00:02.766 [info] trigger 学习库/Deep learning/概念库/评价指标.md resolve  [object Object] 
2025-08-01 09:00:02.767 [info] index finished after resolve  [object Object] 
2025-08-01 09:00:02.768 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:01:12.765 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 09:01:12.780 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 09:01:12.788 [info] index finished after resolve  [object Object] 
2025-08-01 09:01:12.789 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:01:36.621 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 09:01:36.643 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 09:01:36.643 [info] index finished after resolve  [object Object] 
2025-08-01 09:01:36.644 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:01:39.109 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 09:01:39.128 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 09:01:39.129 [info] index finished after resolve  [object Object] 
2025-08-01 09:01:39.130 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:02:42.826 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 09:02:42.859 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 09:02:42.861 [info] index finished after resolve  [object Object] 
2025-08-01 09:02:42.862 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:04:04.774 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 09:04:04.781 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 09:04:04.783 [info] index finished after resolve  [object Object] 
2025-08-01 09:04:04.783 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:04:06.825 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 09:04:06.832 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 09:04:06.833 [info] index finished after resolve  [object Object] 
2025-08-01 09:04:06.833 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:04:09.040 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 09:04:09.048 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 09:04:09.049 [info] index finished after resolve  [object Object] 
2025-08-01 09:04:09.049 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:04:11.113 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 09:04:11.122 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 09:04:11.123 [info] index finished after resolve  [object Object] 
2025-08-01 09:04:11.123 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:04:15.430 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 09:04:15.439 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 09:04:15.440 [info] index finished after resolve  [object Object] 
2025-08-01 09:04:15.440 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:04:17.523 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 09:04:17.532 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 09:04:17.533 [info] index finished after resolve  [object Object] 
2025-08-01 09:04:17.534 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:04:20.661 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 09:04:20.668 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 09:04:20.670 [info] index finished after resolve  [object Object] 
2025-08-01 09:04:20.671 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:04:23.546 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 09:04:23.554 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 09:04:23.555 [info] index finished after resolve  [object Object] 
2025-08-01 09:04:23.555 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:04:26.326 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 09:04:26.332 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 09:04:26.333 [info] index finished after resolve  [object Object] 
2025-08-01 09:04:26.333 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:04:28.507 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 09:04:28.514 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 09:04:28.515 [info] index finished after resolve  [object Object] 
2025-08-01 09:04:28.516 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:04:31.446 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 09:04:31.453 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 09:04:31.454 [info] index finished after resolve  [object Object] 
2025-08-01 09:04:31.455 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:04:33.810 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 09:04:33.818 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 09:04:33.819 [info] index finished after resolve  [object Object] 
2025-08-01 09:04:33.820 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:04:36.865 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 09:04:36.874 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 09:04:36.875 [info] index finished after resolve  [object Object] 
2025-08-01 09:04:36.876 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:04:43.080 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 09:04:43.091 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 09:04:43.091 [info] index finished after resolve  [object Object] 
2025-08-01 09:04:43.092 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:04:50.139 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 09:04:50.144 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 09:04:50.145 [info] index finished after resolve  [object Object] 
2025-08-01 09:04:50.145 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:04:52.208 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 09:04:52.215 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 09:04:52.216 [info] index finished after resolve  [object Object] 
2025-08-01 09:04:52.217 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:04:54.481 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 09:04:54.488 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 09:04:54.489 [info] index finished after resolve  [object Object] 
2025-08-01 09:04:54.490 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:04:56.943 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 09:04:56.951 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 09:04:56.951 [info] index finished after resolve  [object Object] 
2025-08-01 09:04:56.952 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:04:59.000 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 09:04:59.007 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 09:04:59.008 [info] index finished after resolve  [object Object] 
2025-08-01 09:04:59.008 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:05:01.990 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 09:05:01.996 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 09:05:01.997 [info] index finished after resolve  [object Object] 
2025-08-01 09:05:01.997 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:05:06.490 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 09:05:06.498 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 09:05:06.499 [info] index finished after resolve  [object Object] 
2025-08-01 09:05:06.500 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:05:59.115 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 09:05:59.136 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 09:05:59.139 [info] index finished after resolve  [object Object] 
2025-08-01 09:05:59.140 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:07:19.642 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 09:07:19.664 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 09:07:19.666 [info] index finished after resolve  [object Object] 
2025-08-01 09:07:19.666 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:13:18.042 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md   
2025-08-01 09:13:18.056 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md resolve  [object Object] 
2025-08-01 09:13:18.061 [info] index finished after resolve  [object Object] 
2025-08-01 09:13:18.062 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:13:58.703 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md   
2025-08-01 09:13:58.709 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md resolve  [object Object] 
2025-08-01 09:13:58.716 [info] index finished after resolve  [object Object] 
2025-08-01 09:13:58.716 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:14:23.793 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md   
2025-08-01 09:14:23.803 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md resolve  [object Object] 
2025-08-01 09:14:23.811 [info] index finished after resolve  [object Object] 
2025-08-01 09:14:23.811 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:14:39.796 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md   
2025-08-01 09:14:39.804 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md resolve  [object Object] 
2025-08-01 09:14:39.811 [info] index finished after resolve  [object Object] 
2025-08-01 09:14:39.811 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:15:53.425 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md   
2025-08-01 09:15:53.433 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md resolve  [object Object] 
2025-08-01 09:15:53.440 [info] index finished after resolve  [object Object] 
2025-08-01 09:15:53.441 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:16:23.425 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md   
2025-08-01 09:16:23.432 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md resolve  [object Object] 
2025-08-01 09:16:23.440 [info] index finished after resolve  [object Object] 
2025-08-01 09:16:23.441 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:16:48.295 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md   
2025-08-01 09:16:48.303 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md resolve  [object Object] 
2025-08-01 09:16:48.311 [info] index finished after resolve  [object Object] 
2025-08-01 09:16:48.311 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:17:20.932 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md   
2025-08-01 09:17:20.939 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md resolve  [object Object] 
2025-08-01 09:17:20.946 [info] index finished after resolve  [object Object] 
2025-08-01 09:17:20.946 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:17:43.652 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md   
2025-08-01 09:17:43.660 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md resolve  [object Object] 
2025-08-01 09:17:43.667 [info] index finished after resolve  [object Object] 
2025-08-01 09:17:43.668 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:18:14.454 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md   
2025-08-01 09:18:14.462 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md resolve  [object Object] 
2025-08-01 09:18:14.470 [info] index finished after resolve  [object Object] 
2025-08-01 09:18:14.471 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:18:33.540 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md   
2025-08-01 09:18:33.550 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md resolve  [object Object] 
2025-08-01 09:18:33.559 [info] index finished after resolve  [object Object] 
2025-08-01 09:18:33.559 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:18:54.130 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md   
2025-08-01 09:18:54.138 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md resolve  [object Object] 
2025-08-01 09:18:54.148 [info] index finished after resolve  [object Object] 
2025-08-01 09:18:54.149 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:19:09.400 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md   
2025-08-01 09:19:09.408 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md resolve  [object Object] 
2025-08-01 09:19:09.416 [info] index finished after resolve  [object Object] 
2025-08-01 09:19:09.417 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:19:44.625 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md   
2025-08-01 09:19:44.632 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md resolve  [object Object] 
2025-08-01 09:19:44.641 [info] index finished after resolve  [object Object] 
2025-08-01 09:19:44.641 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:20:13.515 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md   
2025-08-01 09:20:13.525 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md resolve  [object Object] 
2025-08-01 09:20:13.535 [info] index finished after resolve  [object Object] 
2025-08-01 09:20:13.536 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:20:30.832 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md   
2025-08-01 09:20:30.840 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md resolve  [object Object] 
2025-08-01 09:20:30.850 [info] index finished after resolve  [object Object] 
2025-08-01 09:20:30.850 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:38:04.468 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:38:04.476 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:38:04.478 [info] index finished after resolve  [object Object] 
2025-08-01 09:38:04.478 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:38:07.258 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:38:07.263 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:38:07.265 [info] index finished after resolve  [object Object] 
2025-08-01 09:38:07.265 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:38:09.289 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:38:09.297 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:38:09.298 [info] index finished after resolve  [object Object] 
2025-08-01 09:38:09.299 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:38:12.035 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:38:12.041 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:38:12.042 [info] index finished after resolve  [object Object] 
2025-08-01 09:38:12.043 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:38:14.069 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:38:14.072 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:38:14.072 [info] index finished after resolve  [object Object] 
2025-08-01 09:38:14.073 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:38:21.467 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:38:21.473 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:38:21.474 [info] index finished after resolve  [object Object] 
2025-08-01 09:38:21.475 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:38:24.832 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:38:24.840 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:38:24.842 [info] index finished after resolve  [object Object] 
2025-08-01 09:38:24.842 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:38:26.910 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:38:26.919 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:38:26.920 [info] index finished after resolve  [object Object] 
2025-08-01 09:38:26.921 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:38:30.818 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:38:30.826 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:38:30.827 [info] index finished after resolve  [object Object] 
2025-08-01 09:38:30.828 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:38:33.517 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:38:33.522 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:38:33.524 [info] index finished after resolve  [object Object] 
2025-08-01 09:38:33.525 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:38:35.674 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:38:35.683 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:38:35.684 [info] index finished after resolve  [object Object] 
2025-08-01 09:38:35.684 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:38:41.501 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:38:41.508 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:38:41.509 [info] index finished after resolve  [object Object] 
2025-08-01 09:38:41.510 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:38:43.640 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:38:43.648 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:38:43.649 [info] index finished after resolve  [object Object] 
2025-08-01 09:38:43.650 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:38:47.875 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:38:47.881 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:38:47.882 [info] index finished after resolve  [object Object] 
2025-08-01 09:38:47.883 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:39:01.931 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:39:01.938 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:39:01.939 [info] index finished after resolve  [object Object] 
2025-08-01 09:39:01.940 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:39:04.231 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:39:04.244 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:39:04.245 [info] index finished after resolve  [object Object] 
2025-08-01 09:39:04.246 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:39:06.254 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:39:06.261 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:39:06.262 [info] index finished after resolve  [object Object] 
2025-08-01 09:39:06.263 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:39:08.361 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:39:08.368 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:39:08.369 [info] index finished after resolve  [object Object] 
2025-08-01 09:39:08.369 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:39:52.324 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:39:52.332 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:39:52.334 [info] index finished after resolve  [object Object] 
2025-08-01 09:39:52.334 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:39:56.364 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:39:56.373 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:39:56.374 [info] index finished after resolve  [object Object] 
2025-08-01 09:39:56.375 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:40:01.901 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:40:01.908 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:40:01.910 [info] index finished after resolve  [object Object] 
2025-08-01 09:40:01.910 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:40:03.997 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:40:04.004 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:40:04.005 [info] index finished after resolve  [object Object] 
2025-08-01 09:40:04.006 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:40:10.671 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:40:10.678 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:40:10.679 [info] index finished after resolve  [object Object] 
2025-08-01 09:40:10.680 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:40:13.080 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:40:13.087 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:40:13.089 [info] index finished after resolve  [object Object] 
2025-08-01 09:40:13.089 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:40:15.751 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:40:15.758 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:40:15.759 [info] index finished after resolve  [object Object] 
2025-08-01 09:40:15.760 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:40:22.490 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:40:22.497 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:40:22.499 [info] index finished after resolve  [object Object] 
2025-08-01 09:40:22.500 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:40:25.280 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:40:25.285 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:40:25.286 [info] index finished after resolve  [object Object] 
2025-08-01 09:40:25.286 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:40:27.627 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:40:27.635 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:40:27.636 [info] index finished after resolve  [object Object] 
2025-08-01 09:40:27.637 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:40:29.959 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:40:29.969 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:40:29.969 [info] index finished after resolve  [object Object] 
2025-08-01 09:40:29.970 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:40:32.107 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:40:32.114 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:40:32.115 [info] index finished after resolve  [object Object] 
2025-08-01 09:40:32.116 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:40:34.455 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:40:34.461 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:40:34.463 [info] index finished after resolve  [object Object] 
2025-08-01 09:40:34.463 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:40:36.917 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:40:36.922 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:40:36.924 [info] index finished after resolve  [object Object] 
2025-08-01 09:40:36.924 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:40:45.721 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:40:45.728 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:40:45.729 [info] index finished after resolve  [object Object] 
2025-08-01 09:40:45.730 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:40:56.151 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:40:56.163 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:40:56.164 [info] index finished after resolve  [object Object] 
2025-08-01 09:40:56.165 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:40:58.318 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:40:58.325 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:40:58.326 [info] index finished after resolve  [object Object] 
2025-08-01 09:40:58.327 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:41:00.354 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:41:00.361 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:41:00.362 [info] index finished after resolve  [object Object] 
2025-08-01 09:41:00.362 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:41:04.876 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:41:04.884 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:41:04.886 [info] index finished after resolve  [object Object] 
2025-08-01 09:41:04.887 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:41:07.032 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:41:07.039 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:41:07.041 [info] index finished after resolve  [object Object] 
2025-08-01 09:41:07.042 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:41:09.057 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:41:09.063 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:41:09.064 [info] index finished after resolve  [object Object] 
2025-08-01 09:41:09.064 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:41:11.484 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:41:11.491 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:41:11.493 [info] index finished after resolve  [object Object] 
2025-08-01 09:41:11.493 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:41:22.961 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:41:22.968 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:41:22.969 [info] index finished after resolve  [object Object] 
2025-08-01 09:41:22.970 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:41:25.309 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:41:25.314 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:41:25.316 [info] index finished after resolve  [object Object] 
2025-08-01 09:41:25.317 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:41:27.421 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:41:27.426 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:41:27.427 [info] index finished after resolve  [object Object] 
2025-08-01 09:41:27.428 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:41:29.901 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:41:29.907 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:41:29.908 [info] index finished after resolve  [object Object] 
2025-08-01 09:41:29.909 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:41:32.001 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:41:32.008 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:41:32.009 [info] index finished after resolve  [object Object] 
2025-08-01 09:41:32.010 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:41:34.078 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:41:34.084 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:41:34.085 [info] index finished after resolve  [object Object] 
2025-08-01 09:41:34.086 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:41:38.325 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:41:38.367 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:41:38.371 [info] index finished after resolve  [object Object] 
2025-08-01 09:41:38.372 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:41:48.451 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:41:48.456 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:41:48.457 [info] index finished after resolve  [object Object] 
2025-08-01 09:41:48.458 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:41:51.846 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:41:51.850 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:41:51.851 [info] index finished after resolve  [object Object] 
2025-08-01 09:41:51.852 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:41:55.608 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:41:55.613 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:41:55.615 [info] index finished after resolve  [object Object] 
2025-08-01 09:41:55.615 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:41:57.670 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:41:57.677 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:41:57.677 [info] index finished after resolve  [object Object] 
2025-08-01 09:41:57.678 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:42:00.973 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:42:00.978 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:42:00.979 [info] index finished after resolve  [object Object] 
2025-08-01 09:42:00.979 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:42:03.030 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:42:03.037 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:42:03.039 [info] index finished after resolve  [object Object] 
2025-08-01 09:42:03.039 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:42:06.955 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:42:06.959 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:42:06.960 [info] index finished after resolve  [object Object] 
2025-08-01 09:42:06.961 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:42:09.145 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:42:09.153 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:42:09.153 [info] index finished after resolve  [object Object] 
2025-08-01 09:42:09.154 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:42:11.459 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:42:11.465 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:42:11.467 [info] index finished after resolve  [object Object] 
2025-08-01 09:42:11.468 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:42:21.560 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:42:21.567 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:42:21.569 [info] index finished after resolve  [object Object] 
2025-08-01 09:42:21.569 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:42:23.637 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:42:23.642 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:42:23.643 [info] index finished after resolve  [object Object] 
2025-08-01 09:42:23.643 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:42:29.932 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:42:29.938 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:42:29.939 [info] index finished after resolve  [object Object] 
2025-08-01 09:42:29.939 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:42:32.913 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:42:32.920 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:42:32.921 [info] index finished after resolve  [object Object] 
2025-08-01 09:42:32.922 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:42:36.176 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:42:36.182 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:42:36.184 [info] index finished after resolve  [object Object] 
2025-08-01 09:42:36.184 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:42:39.600 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:42:39.607 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:42:39.609 [info] index finished after resolve  [object Object] 
2025-08-01 09:42:39.609 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:42:42.270 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:42:42.276 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:42:42.276 [info] index finished after resolve  [object Object] 
2025-08-01 09:42:42.277 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:42:44.519 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:42:44.524 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:42:44.525 [info] index finished after resolve  [object Object] 
2025-08-01 09:42:44.525 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:42:46.655 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:42:46.660 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:42:46.661 [info] index finished after resolve  [object Object] 
2025-08-01 09:42:46.661 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:42:49.478 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:42:49.485 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:42:49.487 [info] index finished after resolve  [object Object] 
2025-08-01 09:42:49.487 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:42:51.799 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:42:51.805 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:42:51.806 [info] index finished after resolve  [object Object] 
2025-08-01 09:42:51.807 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:42:57.251 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:42:57.257 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:42:57.259 [info] index finished after resolve  [object Object] 
2025-08-01 09:42:57.260 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:42:59.327 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:42:59.334 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:42:59.336 [info] index finished after resolve  [object Object] 
2025-08-01 09:42:59.336 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:43:01.430 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:43:01.436 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:43:01.437 [info] index finished after resolve  [object Object] 
2025-08-01 09:43:01.438 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:43:08.461 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:43:08.467 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:43:08.468 [info] index finished after resolve  [object Object] 
2025-08-01 09:43:08.469 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:43:10.963 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:43:10.972 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:43:10.973 [info] index finished after resolve  [object Object] 
2025-08-01 09:43:10.974 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:43:13.339 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:43:13.344 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:43:13.345 [info] index finished after resolve  [object Object] 
2025-08-01 09:43:13.345 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:43:15.523 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:43:15.529 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:43:15.530 [info] index finished after resolve  [object Object] 
2025-08-01 09:43:15.530 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:43:17.601 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:43:17.614 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:43:17.615 [info] index finished after resolve  [object Object] 
2025-08-01 09:43:17.616 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:43:19.600 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:43:19.609 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:43:19.610 [info] index finished after resolve  [object Object] 
2025-08-01 09:43:19.610 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:43:29.400 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:43:29.409 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:43:29.410 [info] index finished after resolve  [object Object] 
2025-08-01 09:43:29.411 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:43:59.577 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:43:59.582 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:43:59.583 [info] index finished after resolve  [object Object] 
2025-08-01 09:43:59.583 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:44:04.651 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:44:04.658 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:44:04.659 [info] index finished after resolve  [object Object] 
2025-08-01 09:44:04.660 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:44:07.730 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:44:07.736 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:44:07.737 [info] index finished after resolve  [object Object] 
2025-08-01 09:44:07.738 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:44:10.174 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:44:10.180 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:44:10.180 [info] index finished after resolve  [object Object] 
2025-08-01 09:44:10.181 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:44:12.588 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:44:12.594 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:44:12.595 [info] index finished after resolve  [object Object] 
2025-08-01 09:44:12.595 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:44:15.765 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:44:15.772 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:44:15.773 [info] index finished after resolve  [object Object] 
2025-08-01 09:44:15.774 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:44:18.042 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:44:18.049 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:44:18.050 [info] index finished after resolve  [object Object] 
2025-08-01 09:44:18.051 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:44:22.558 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:44:22.562 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:44:22.563 [info] index finished after resolve  [object Object] 
2025-08-01 09:44:22.564 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:44:37.432 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:44:37.438 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:44:37.439 [info] index finished after resolve  [object Object] 
2025-08-01 09:44:37.440 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:44:40.913 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:44:40.922 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:44:40.924 [info] index finished after resolve  [object Object] 
2025-08-01 09:44:40.925 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:44:43.593 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:44:43.600 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:44:43.601 [info] index finished after resolve  [object Object] 
2025-08-01 09:44:43.602 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:44:45.589 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:44:45.595 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:44:45.596 [info] index finished after resolve  [object Object] 
2025-08-01 09:44:45.597 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:44:48.284 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:44:48.290 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:44:48.291 [info] index finished after resolve  [object Object] 
2025-08-01 09:44:48.292 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:44:50.333 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:44:50.340 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:44:50.341 [info] index finished after resolve  [object Object] 
2025-08-01 09:44:50.342 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:44:53.085 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:44:53.093 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:44:53.094 [info] index finished after resolve  [object Object] 
2025-08-01 09:44:53.095 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:44:55.280 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:44:55.288 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:44:55.289 [info] index finished after resolve  [object Object] 
2025-08-01 09:44:55.289 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:45:04.445 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:45:04.451 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:45:04.452 [info] index finished after resolve  [object Object] 
2025-08-01 09:45:04.453 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:45:14.127 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:45:14.134 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:45:14.135 [info] index finished after resolve  [object Object] 
2025-08-01 09:45:14.136 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:45:20.056 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:45:20.061 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:45:20.062 [info] index finished after resolve  [object Object] 
2025-08-01 09:45:20.063 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:45:25.023 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:45:25.029 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:45:25.030 [info] index finished after resolve  [object Object] 
2025-08-01 09:45:25.031 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:45:28.562 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:45:28.569 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:45:28.570 [info] index finished after resolve  [object Object] 
2025-08-01 09:45:28.571 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:45:39.186 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:45:39.192 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:45:39.194 [info] index finished after resolve  [object Object] 
2025-08-01 09:45:39.195 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:45:44.436 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:45:44.444 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:45:44.446 [info] index finished after resolve  [object Object] 
2025-08-01 09:45:44.447 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:46:03.627 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:46:03.632 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:46:03.633 [info] index finished after resolve  [object Object] 
2025-08-01 09:46:03.634 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:46:13.006 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:46:13.009 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:46:13.010 [info] index finished after resolve  [object Object] 
2025-08-01 09:46:13.011 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:46:18.750 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:46:18.755 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:46:18.756 [info] index finished after resolve  [object Object] 
2025-08-01 09:46:18.757 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:46:25.227 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:46:25.232 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:46:25.233 [info] index finished after resolve  [object Object] 
2025-08-01 09:46:25.234 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:46:33.392 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:46:33.397 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:46:33.399 [info] index finished after resolve  [object Object] 
2025-08-01 09:46:33.399 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:46:58.952 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:46:58.959 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:46:58.960 [info] index finished after resolve  [object Object] 
2025-08-01 09:46:58.961 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:47:01.020 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:47:01.024 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:47:01.026 [info] index finished after resolve  [object Object] 
2025-08-01 09:47:01.026 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:47:03.592 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:47:03.597 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:47:03.598 [info] index finished after resolve  [object Object] 
2025-08-01 09:47:03.598 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:47:12.978 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:47:12.984 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:47:12.985 [info] index finished after resolve  [object Object] 
2025-08-01 09:47:12.985 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:47:15.144 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:47:15.148 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:47:15.150 [info] index finished after resolve  [object Object] 
2025-08-01 09:47:15.150 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:47:17.202 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:47:17.208 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:47:17.209 [info] index finished after resolve  [object Object] 
2025-08-01 09:47:17.210 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:47:20.180 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:47:20.187 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:47:20.188 [info] index finished after resolve  [object Object] 
2025-08-01 09:47:20.189 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:48:04.967 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:48:04.972 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:48:04.974 [info] index finished after resolve  [object Object] 
2025-08-01 09:48:04.975 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:48:07.096 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:48:07.101 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:48:07.103 [info] index finished after resolve  [object Object] 
2025-08-01 09:48:07.104 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:48:09.173 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:48:09.182 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:48:09.183 [info] index finished after resolve  [object Object] 
2025-08-01 09:48:09.183 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:48:11.298 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:48:11.303 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:48:11.304 [info] index finished after resolve  [object Object] 
2025-08-01 09:48:11.305 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:48:13.352 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:48:13.360 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:48:13.361 [info] index finished after resolve  [object Object] 
2025-08-01 09:48:13.362 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:48:19.155 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:48:19.162 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:48:19.164 [info] index finished after resolve  [object Object] 
2025-08-01 09:48:19.164 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:48:31.536 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:48:31.543 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:48:31.544 [info] index finished after resolve  [object Object] 
2025-08-01 09:48:31.545 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:48:34.570 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:48:34.578 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:48:34.579 [info] index finished after resolve  [object Object] 
2025-08-01 09:48:34.580 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:53:09.492 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:53:09.502 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:53:09.503 [info] index finished after resolve  [object Object] 
2025-08-01 09:53:09.503 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:53:14.907 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:53:14.911 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:53:14.912 [info] index finished after resolve  [object Object] 
2025-08-01 09:53:14.913 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:53:16.991 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:53:16.996 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:53:16.998 [info] index finished after resolve  [object Object] 
2025-08-01 09:53:16.999 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:53:19.223 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:53:19.228 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:53:19.229 [info] index finished after resolve  [object Object] 
2025-08-01 09:53:19.230 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:53:23.788 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:53:23.793 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:53:23.794 [info] index finished after resolve  [object Object] 
2025-08-01 09:53:23.795 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:53:26.301 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:53:26.308 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:53:26.309 [info] index finished after resolve  [object Object] 
2025-08-01 09:53:26.309 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:53:30.707 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:53:30.714 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:53:30.716 [info] index finished after resolve  [object Object] 
2025-08-01 09:53:30.717 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:53:33.172 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:53:33.178 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:53:33.180 [info] index finished after resolve  [object Object] 
2025-08-01 09:53:33.181 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:53:41.214 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:53:41.225 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:53:41.226 [info] index finished after resolve  [object Object] 
2025-08-01 09:53:41.227 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:53:44.044 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:53:44.050 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:53:44.052 [info] index finished after resolve  [object Object] 
2025-08-01 09:53:44.052 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:53:46.334 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:53:46.339 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:53:46.340 [info] index finished after resolve  [object Object] 
2025-08-01 09:53:46.340 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:53:49.679 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:53:49.686 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:53:49.687 [info] index finished after resolve  [object Object] 
2025-08-01 09:53:49.687 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:53:59.709 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:53:59.721 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:53:59.722 [info] index finished after resolve  [object Object] 
2025-08-01 09:53:59.722 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:54:01.712 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:54:01.719 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:54:01.720 [info] index finished after resolve  [object Object] 
2025-08-01 09:54:01.721 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:54:03.896 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:54:03.903 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:54:03.905 [info] index finished after resolve  [object Object] 
2025-08-01 09:54:03.905 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:54:06.204 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:54:06.209 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:54:06.210 [info] index finished after resolve  [object Object] 
2025-08-01 09:54:06.210 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:54:08.348 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:54:08.356 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:54:08.357 [info] index finished after resolve  [object Object] 
2025-08-01 09:54:08.358 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:54:13.207 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:54:13.213 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:54:13.214 [info] index finished after resolve  [object Object] 
2025-08-01 09:54:13.214 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:54:27.087 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:54:27.095 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:54:27.096 [info] index finished after resolve  [object Object] 
2025-08-01 09:54:27.096 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:54:40.244 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:54:40.252 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:54:40.254 [info] index finished after resolve  [object Object] 
2025-08-01 09:54:40.254 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:54:42.263 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:54:42.272 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:54:42.273 [info] index finished after resolve  [object Object] 
2025-08-01 09:54:42.274 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:54:44.342 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:54:44.351 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:54:44.352 [info] index finished after resolve  [object Object] 
2025-08-01 09:54:44.353 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:54:47.022 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:54:47.029 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:54:47.030 [info] index finished after resolve  [object Object] 
2025-08-01 09:54:47.031 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:55:02.829 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:55:02.836 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:55:02.837 [info] index finished after resolve  [object Object] 
2025-08-01 09:55:02.838 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:55:05.324 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:55:05.331 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:55:05.332 [info] index finished after resolve  [object Object] 
2025-08-01 09:55:05.332 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:55:07.439 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:55:07.446 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:55:07.446 [info] index finished after resolve  [object Object] 
2025-08-01 09:55:07.447 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:55:27.358 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:55:27.364 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:55:27.365 [info] index finished after resolve  [object Object] 
2025-08-01 09:55:27.366 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 09:55:44.641 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 09:55:44.647 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 09:55:44.648 [info] index finished after resolve  [object Object] 
2025-08-01 09:55:44.648 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:02:43.583 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:02:43.590 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:02:43.591 [info] index finished after resolve  [object Object] 
2025-08-01 10:02:43.592 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:02:46.157 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:02:46.165 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:02:46.166 [info] index finished after resolve  [object Object] 
2025-08-01 10:02:46.167 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:03:03.450 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:03:03.456 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:03:03.457 [info] index finished after resolve  [object Object] 
2025-08-01 10:03:03.458 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:03:05.663 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:03:05.671 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:03:05.672 [info] index finished after resolve  [object Object] 
2025-08-01 10:03:05.673 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:03:07.803 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:03:07.808 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:03:07.809 [info] index finished after resolve  [object Object] 
2025-08-01 10:03:07.809 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:03:11.220 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:03:11.229 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:03:11.229 [info] index finished after resolve  [object Object] 
2025-08-01 10:03:11.230 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:03:16.217 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:03:16.222 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:03:16.223 [info] index finished after resolve  [object Object] 
2025-08-01 10:03:16.223 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:03:25.368 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:03:25.374 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:03:25.375 [info] index finished after resolve  [object Object] 
2025-08-01 10:03:25.375 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:03:28.119 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:03:28.125 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:03:28.126 [info] index finished after resolve  [object Object] 
2025-08-01 10:03:28.127 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:03:30.682 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:03:30.686 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:03:30.687 [info] index finished after resolve  [object Object] 
2025-08-01 10:03:30.687 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:03:34.120 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:03:34.126 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:03:34.128 [info] index finished after resolve  [object Object] 
2025-08-01 10:03:34.129 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:03:36.397 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:03:36.401 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:03:36.402 [info] index finished after resolve  [object Object] 
2025-08-01 10:03:36.403 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:03:38.423 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:03:38.429 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:03:38.430 [info] index finished after resolve  [object Object] 
2025-08-01 10:03:38.431 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:03:40.457 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:03:40.467 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:03:40.468 [info] index finished after resolve  [object Object] 
2025-08-01 10:03:40.469 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:03:42.533 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:03:42.540 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:03:42.543 [info] index finished after resolve  [object Object] 
2025-08-01 10:03:42.544 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:03:51.156 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:03:51.164 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:03:51.165 [info] index finished after resolve  [object Object] 
2025-08-01 10:03:51.166 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:03:53.314 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:03:53.319 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:03:53.320 [info] index finished after resolve  [object Object] 
2025-08-01 10:03:53.321 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:03:55.486 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:03:55.491 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:03:55.493 [info] index finished after resolve  [object Object] 
2025-08-01 10:03:55.494 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:03:57.660 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:03:57.668 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:03:57.669 [info] index finished after resolve  [object Object] 
2025-08-01 10:03:57.669 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:04:02.151 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:04:02.158 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:04:02.160 [info] index finished after resolve  [object Object] 
2025-08-01 10:04:02.161 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:04:04.203 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:04:04.208 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:04:04.209 [info] index finished after resolve  [object Object] 
2025-08-01 10:04:04.209 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:04:06.469 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:04:06.475 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:04:06.477 [info] index finished after resolve  [object Object] 
2025-08-01 10:04:06.477 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:04:08.672 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:04:08.681 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:04:08.682 [info] index finished after resolve  [object Object] 
2025-08-01 10:04:08.682 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:04:12.034 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:04:12.043 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:04:12.044 [info] index finished after resolve  [object Object] 
2025-08-01 10:04:12.044 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:04:14.328 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:04:14.336 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:04:14.338 [info] index finished after resolve  [object Object] 
2025-08-01 10:04:14.339 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:04:22.835 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:04:22.840 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:04:22.841 [info] index finished after resolve  [object Object] 
2025-08-01 10:04:22.842 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:04:29.443 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:04:29.450 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:04:29.451 [info] index finished after resolve  [object Object] 
2025-08-01 10:04:29.452 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:04:31.506 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:04:31.511 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:04:31.513 [info] index finished after resolve  [object Object] 
2025-08-01 10:04:31.514 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:04:34.931 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:04:34.937 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:04:34.938 [info] index finished after resolve  [object Object] 
2025-08-01 10:04:34.938 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:04:37.652 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:04:37.661 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:04:37.664 [info] index finished after resolve  [object Object] 
2025-08-01 10:04:37.665 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:04:47.484 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:04:47.491 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:04:47.492 [info] index finished after resolve  [object Object] 
2025-08-01 10:04:47.493 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:04:53.266 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:04:53.271 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:04:53.272 [info] index finished after resolve  [object Object] 
2025-08-01 10:04:53.272 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:04:56.590 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:04:56.593 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:04:56.594 [info] index finished after resolve  [object Object] 
2025-08-01 10:04:56.594 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:04:59.936 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:04:59.941 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:04:59.942 [info] index finished after resolve  [object Object] 
2025-08-01 10:04:59.942 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:05:02.393 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:05:02.401 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:05:02.402 [info] index finished after resolve  [object Object] 
2025-08-01 10:05:02.403 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:05:05.159 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:05:05.166 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:05:05.167 [info] index finished after resolve  [object Object] 
2025-08-01 10:05:05.167 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:05:07.537 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:05:07.542 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:05:07.543 [info] index finished after resolve  [object Object] 
2025-08-01 10:05:07.543 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:05:15.690 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:05:15.699 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:05:15.700 [info] index finished after resolve  [object Object] 
2025-08-01 10:05:15.701 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:05:18.301 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:05:18.307 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:05:18.308 [info] index finished after resolve  [object Object] 
2025-08-01 10:05:18.308 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:05:22.897 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:05:22.904 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:05:22.905 [info] index finished after resolve  [object Object] 
2025-08-01 10:05:22.905 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:05:27.065 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:05:27.069 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:05:27.071 [info] index finished after resolve  [object Object] 
2025-08-01 10:05:27.071 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:05:29.523 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:05:29.532 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:05:29.532 [info] index finished after resolve  [object Object] 
2025-08-01 10:05:29.533 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:05:32.061 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:05:32.069 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:05:32.070 [info] index finished after resolve  [object Object] 
2025-08-01 10:05:32.070 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:05:42.106 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:05:42.114 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:05:42.115 [info] index finished after resolve  [object Object] 
2025-08-01 10:05:42.116 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:05:46.594 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:05:46.601 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:05:46.603 [info] index finished after resolve  [object Object] 
2025-08-01 10:05:46.603 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:05:48.625 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:05:48.633 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:05:48.634 [info] index finished after resolve  [object Object] 
2025-08-01 10:05:48.634 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:05:50.768 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:05:50.774 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:05:50.775 [info] index finished after resolve  [object Object] 
2025-08-01 10:05:50.775 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:05:53.233 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:05:53.239 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:05:53.240 [info] index finished after resolve  [object Object] 
2025-08-01 10:05:53.241 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:06:10.904 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:06:10.910 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:06:10.911 [info] index finished after resolve  [object Object] 
2025-08-01 10:06:10.912 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:06:12.944 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:06:12.952 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:06:12.954 [info] index finished after resolve  [object Object] 
2025-08-01 10:06:12.955 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:06:16.176 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:06:16.184 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:06:16.185 [info] index finished after resolve  [object Object] 
2025-08-01 10:06:16.185 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:06:18.314 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:06:18.323 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:06:18.324 [info] index finished after resolve  [object Object] 
2025-08-01 10:06:18.324 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:06:20.998 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:06:21.004 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:06:21.005 [info] index finished after resolve  [object Object] 
2025-08-01 10:06:21.005 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:06:28.161 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:06:28.168 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:06:28.170 [info] index finished after resolve  [object Object] 
2025-08-01 10:06:28.171 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:06:30.397 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:06:30.405 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:06:30.407 [info] index finished after resolve  [object Object] 
2025-08-01 10:06:30.407 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:06:57.849 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:06:57.855 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:06:57.856 [info] index finished after resolve  [object Object] 
2025-08-01 10:06:57.857 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:07:07.905 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:07:07.910 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:07:07.911 [info] index finished after resolve  [object Object] 
2025-08-01 10:07:07.912 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:07:10.532 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:07:10.540 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:07:10.541 [info] index finished after resolve  [object Object] 
2025-08-01 10:07:10.542 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:07:13.090 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:07:13.096 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:07:13.098 [info] index finished after resolve  [object Object] 
2025-08-01 10:07:13.098 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:07:51.912 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:07:51.917 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:07:51.919 [info] index finished after resolve  [object Object] 
2025-08-01 10:07:51.919 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:10:04.173 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:10:04.179 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:10:04.181 [info] index finished after resolve  [object Object] 
2025-08-01 10:10:04.182 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:11:06.500 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:11:06.509 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:11:06.510 [info] index finished after resolve  [object Object] 
2025-08-01 10:11:06.511 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:11:08.649 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:11:08.655 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:11:08.656 [info] index finished after resolve  [object Object] 
2025-08-01 10:11:08.657 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:11:11.660 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:11:11.666 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:11:11.667 [info] index finished after resolve  [object Object] 
2025-08-01 10:11:11.668 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:11:16.433 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:11:16.440 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:11:16.441 [info] index finished after resolve  [object Object] 
2025-08-01 10:11:16.441 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:11:18.533 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:11:18.535 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:11:18.537 [info] index finished after resolve  [object Object] 
2025-08-01 10:11:18.538 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:11:22.997 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:11:23.003 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:11:23.005 [info] index finished after resolve  [object Object] 
2025-08-01 10:11:23.005 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:11:30.860 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:11:30.866 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:11:30.867 [info] index finished after resolve  [object Object] 
2025-08-01 10:11:30.868 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:11:35.311 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:11:35.318 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:11:35.319 [info] index finished after resolve  [object Object] 
2025-08-01 10:11:35.319 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:11:37.361 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:11:37.368 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:11:37.369 [info] index finished after resolve  [object Object] 
2025-08-01 10:11:37.369 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:11:39.426 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:11:39.432 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:11:39.433 [info] index finished after resolve  [object Object] 
2025-08-01 10:11:39.434 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:11:41.595 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:11:41.600 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:11:41.601 [info] index finished after resolve  [object Object] 
2025-08-01 10:11:41.602 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:11:45.698 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:11:45.717 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:11:45.718 [info] index finished after resolve  [object Object] 
2025-08-01 10:11:45.718 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:11:47.710 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:11:47.717 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:11:47.717 [info] index finished after resolve  [object Object] 
2025-08-01 10:11:47.718 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:11:49.923 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:11:49.928 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:11:49.929 [info] index finished after resolve  [object Object] 
2025-08-01 10:11:49.930 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:11:52.162 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:11:52.167 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:11:52.168 [info] index finished after resolve  [object Object] 
2025-08-01 10:11:52.169 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:11:54.152 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:11:54.160 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:11:54.161 [info] index finished after resolve  [object Object] 
2025-08-01 10:11:54.162 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:11:56.211 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:11:56.217 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:11:56.218 [info] index finished after resolve  [object Object] 
2025-08-01 10:11:56.219 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:12:01.260 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:12:01.265 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:12:01.266 [info] index finished after resolve  [object Object] 
2025-08-01 10:12:01.267 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:12:03.747 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:12:03.753 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:12:03.754 [info] index finished after resolve  [object Object] 
2025-08-01 10:12:03.754 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:12:05.833 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:12:05.842 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:12:05.844 [info] index finished after resolve  [object Object] 
2025-08-01 10:12:05.844 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:12:58.246 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:12:58.252 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:12:58.253 [info] index finished after resolve  [object Object] 
2025-08-01 10:12:58.254 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:13:34.045 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:13:34.050 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:13:34.053 [info] index finished after resolve  [object Object] 
2025-08-01 10:13:34.053 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:13:36.859 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:13:36.864 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:13:36.865 [info] index finished after resolve  [object Object] 
2025-08-01 10:13:36.866 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:13:39.257 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:13:39.262 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:13:39.264 [info] index finished after resolve  [object Object] 
2025-08-01 10:13:39.265 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:13:41.653 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:13:41.660 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:13:41.661 [info] index finished after resolve  [object Object] 
2025-08-01 10:13:41.661 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:13:44.171 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:13:44.178 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:13:44.179 [info] index finished after resolve  [object Object] 
2025-08-01 10:13:44.179 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:13:46.870 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:13:46.875 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:13:46.877 [info] index finished after resolve  [object Object] 
2025-08-01 10:13:46.877 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:13:49.002 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:13:49.009 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:13:49.010 [info] index finished after resolve  [object Object] 
2025-08-01 10:13:49.011 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:13:51.088 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:13:51.093 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:13:51.094 [info] index finished after resolve  [object Object] 
2025-08-01 10:13:51.095 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:13:58.005 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:13:58.013 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:13:58.014 [info] index finished after resolve  [object Object] 
2025-08-01 10:13:58.015 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:14:03.929 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:14:03.934 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:14:03.935 [info] index finished after resolve  [object Object] 
2025-08-01 10:14:03.936 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:14:14.883 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:14:14.888 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:14:14.890 [info] index finished after resolve  [object Object] 
2025-08-01 10:14:14.891 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:14:19.913 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:14:19.920 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:14:19.921 [info] index finished after resolve  [object Object] 
2025-08-01 10:14:19.922 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:14:22.451 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:14:22.463 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:14:22.464 [info] index finished after resolve  [object Object] 
2025-08-01 10:14:22.465 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:15:00.169 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:15:00.198 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:15:00.200 [info] index finished after resolve  [object Object] 
2025-08-01 10:15:00.200 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:15:38.238 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 10:15:38.445 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 10:15:38.472 [info] index finished after resolve  [object Object] 
2025-08-01 10:15:38.475 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:15:49.420 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 10:15:49.512 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 10:15:49.514 [info] index finished after resolve  [object Object] 
2025-08-01 10:15:49.515 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:15:54.388 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 10:15:54.409 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 10:15:54.478 [info] index finished after resolve  [object Object] 
2025-08-01 10:15:54.479 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:15:57.553 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 10:15:57.624 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 10:15:57.627 [info] index finished after resolve  [object Object] 
2025-08-01 10:15:57.628 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:16:03.453 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 10:16:03.560 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 10:16:03.569 [info] index finished after resolve  [object Object] 
2025-08-01 10:16:03.570 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:16:09.611 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 10:16:09.702 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 10:16:09.703 [info] index finished after resolve  [object Object] 
2025-08-01 10:16:09.703 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:16:30.015 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 10:16:30.020 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 10:16:30.022 [info] index finished after resolve  [object Object] 
2025-08-01 10:16:30.022 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:16:34.066 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 10:16:34.072 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 10:16:34.073 [info] index finished after resolve  [object Object] 
2025-08-01 10:16:34.074 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:17:01.422 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 10:17:01.434 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 10:17:01.435 [info] index finished after resolve  [object Object] 
2025-08-01 10:17:01.436 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:17:11.725 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 10:17:11.734 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 10:17:11.735 [info] index finished after resolve  [object Object] 
2025-08-01 10:17:11.735 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:17:43.274 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 10:17:43.289 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 10:17:43.290 [info] index finished after resolve  [object Object] 
2025-08-01 10:17:43.291 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:18:58.308 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 10:18:58.682 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 10:18:58.684 [info] index finished after resolve  [object Object] 
2025-08-01 10:18:58.685 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:21:49.922 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-01 10:21:49.929 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-01 10:21:49.930 [info] index finished after resolve  [object Object] 
2025-08-01 10:21:49.930 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:23:44.928 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 10:23:44.934 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 10:23:44.935 [info] index finished after resolve  [object Object] 
2025-08-01 10:23:44.935 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:23:46.992 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 10:23:47.001 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 10:23:47.002 [info] index finished after resolve  [object Object] 
2025-08-01 10:23:47.003 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:23:49.110 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 10:23:49.118 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 10:23:49.120 [info] index finished after resolve  [object Object] 
2025-08-01 10:23:49.121 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:23:51.249 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 10:23:51.257 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 10:23:51.258 [info] index finished after resolve  [object Object] 
2025-08-01 10:23:51.259 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:24:03.636 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 10:24:03.643 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 10:24:03.644 [info] index finished after resolve  [object Object] 
2025-08-01 10:24:03.645 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:24:05.865 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 10:24:05.874 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 10:24:05.874 [info] index finished after resolve  [object Object] 
2025-08-01 10:24:05.875 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:24:09.403 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 10:24:09.412 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 10:24:09.412 [info] index finished after resolve  [object Object] 
2025-08-01 10:24:09.413 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:30:51.245 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md   
2025-08-01 10:30:51.259 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md resolve  [object Object] 
2025-08-01 10:30:51.268 [info] index finished after resolve  [object Object] 
2025-08-01 10:30:51.268 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 10:55:05.622 [info] indexing created file att/QuickAdd/scripts/creatExcalidraw.js  [object Object] 
2025-08-01 10:55:05.624 [info] refresh page data from created listeners 0 1022   
