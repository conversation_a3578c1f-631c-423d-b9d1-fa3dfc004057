{"choices": [{"id": "7b0a9cf7-7176-4bbb-acd9-3275e9f66cfe", "name": "文献灵感", "type": "Capture", "command": true, "appendLink": false, "captureTo": "", "captureToActiveFile": true, "createFileIfItDoesntExist": {"enabled": false, "createWithTemplate": false, "template": ""}, "format": {"enabled": true, "format": "\\n{{DATE:YYYY-MM-DD HH:mm}}-{{VALUE}}"}, "insertAfter": {"enabled": false, "after": "", "insertAtEnd": false, "considerSubsections": false, "createIfNotFound": false, "createIfNotFoundLocation": "top"}, "prepend": false, "task": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": false, "openFileInMode": "default"}, {"id": "dcab9dcb-8209-4948-a1a7-5e5c15677d1a", "name": "参考文献", "type": "Capture", "command": true, "appendLink": false, "captureTo": "", "captureToActiveFile": false, "createFileIfItDoesntExist": {"enabled": false, "createWithTemplate": false, "template": ""}, "format": {"enabled": true, "format": "**参考文献**\n- {value}\n"}, "insertAfter": {"enabled": false, "after": "", "insertAtEnd": false, "considerSubsections": false, "createIfNotFound": false, "createIfNotFoundLocation": "top"}, "prepend": false, "task": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": false, "openFileInMode": "default"}, {"id": "37a10e24-c7fd-4530-9a7b-02f998b29c68", "name": "python学习", "type": "Capture", "command": true, "appendLink": false, "captureTo": "", "captureToActiveFile": true, "createFileIfItDoesntExist": {"enabled": false, "createWithTemplate": false, "template": ""}, "format": {"enabled": true, "format": "`````ad-abstract\ntitle: {{VALUE:这个内容的标题是什么呢}}\ncollapse: open\n{{value:可以介绍一下这个内容嘛}}\n````ad-bug\ntitle: 语法\n```\n{{VALUE:请输入它的语法格式把}}\n```\n````\n````ad-hint\ntitle: 例子\n```\n{{value:请给出具体的代码示例吧}}\n```\n````\n\n`````"}, "insertAfter": {"enabled": false, "after": "", "insertAtEnd": false, "considerSubsections": false, "createIfNotFound": false, "createIfNotFoundLocation": "top"}, "prepend": false, "task": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": false, "openFileInMode": "default"}, {"id": "cc4dfb28-1cbb-4051-bbc1-553307264740", "name": "重新加载当前页", "type": "Macro", "command": true, "macroId": "85aa978c-bf79-479c-98aa-70fc6776e303"}, {"id": "359f6a97-3200-48ee-8c65-c77d3f98dda1", "name": "切换阅读、预览、源码模式", "type": "Macro", "command": true, "macroId": "77487bd7-1fc7-4389-ae8e-a7a0f11e7663"}, {"id": "4864da74-40cb-40ce-a4f9-5970b4116d5c", "name": "闪念", "type": "Capture", "command": true, "appendLink": false, "captureTo": "日记库/fleeting_notes/{{DATE:YYYY-MM-DD}}", "captureToActiveFile": false, "createFileIfItDoesntExist": {"enabled": true, "createWithTemplate": true, "template": "日记库/template/fleeting_note.md"}, "format": {"enabled": true, "format": "{{time:hh:mm}} ：{{VALUE:请输入内容}}"}, "insertAfter": {"enabled": false, "after": "## {{date:hh:mm}}", "insertAtEnd": false, "considerSubsections": false, "createIfNotFound": true, "createIfNotFoundLocation": "bottom"}, "prepend": true, "task": true, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": true, "openFileInMode": "default"}, {"id": "21c36254-1c9d-470a-9a5b-f36404acc03d", "name": "工作笔记", "type": "Multi", "command": true, "choices": [{"id": "a2116402-71b3-4cd5-81b0-e73ecbba6937", "name": "创建项目笔记", "type": "Template", "command": true, "templatePath": "工作库/template/比赛模板.md", "fileNameFormat": {"enabled": true, "format": "{{VALUE:请输入项目的名字以创建文件夹}}/{{VALUE: 请输入你要记录的东西}}"}, "folder": {"enabled": true, "folders": ["工作库/项目"], "chooseWhenCreatingNote": false, "createInSameFolderAsActiveFile": false, "chooseFromSubfolders": false}, "appendLink": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": false, "openFileInMode": "default", "fileExistsMode": "Increment the file name", "setFileExistsBehavior": false}, {"id": "271bf115-9836-4eb5-835d-6846f327872f", "name": "创建比赛笔记", "type": "Template", "command": true, "templatePath": "工作库/template/比赛模板.md", "fileNameFormat": {"enabled": true, "format": "{{VALUE:请输入比赛的名字以创建文件夹}}/{{VALUE: 请输入你要记录的东西}}"}, "folder": {"enabled": true, "folders": ["工作库/比赛"], "chooseWhenCreatingNote": false, "createInSameFolderAsActiveFile": false, "chooseFromSubfolders": false}, "appendLink": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": true, "openFileInMode": "source", "fileExistsMode": "Increment the file name", "setFileExistsBehavior": false}, {"id": "9669ad21-248b-492c-b648-c9808d8fdded", "name": "创建通用学习笔记", "type": "Template", "command": true, "templatePath": "学习库/template/通用学习模板.md", "fileNameFormat": {"enabled": true, "format": "{{VALUE:请输入文件夹名}}/{{VALUE:请输入要记录的内容}}"}, "folder": {"enabled": true, "folders": ["学习库"], "chooseWhenCreatingNote": false, "createInSameFolderAsActiveFile": false, "chooseFromSubfolders": false}, "appendLink": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": true, "openFileInMode": "default", "fileExistsMode": "Increment the file name", "setFileExistsBehavior": false}], "collapsed": false}, {"id": "403a1c4a-8ca7-44a1-afad-e41477dba8b1", "name": "制卡", "type": "Multi", "command": true, "choices": [{"id": "ee8b971d-5c95-4d3a-8119-308fd350bb9b", "name": "数学块", "type": "Capture", "command": true, "appendLink": false, "captureTo": "", "captureToActiveFile": true, "createFileIfItDoesntExist": {"enabled": false, "createWithTemplate": false, "template": ""}, "format": {"enabled": true, "format": "```ad-thm\ntitle: {{VALUE:这个数学块的标题是？}}\ncolor: 198,155,64\n{{VALUE:可以输入内容}}\n```"}, "insertAfter": {"enabled": false, "after": "", "insertAtEnd": false, "considerSubsections": false, "createIfNotFound": false, "createIfNotFoundLocation": "top"}, "prepend": false, "task": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": false, "openFileInMode": "default"}, {"id": "c877fbb3-a35a-4ea3-bc73-40e07484dde1", "name": "双列", "type": "Capture", "command": true, "appendLink": false, "captureTo": "", "captureToActiveFile": true, "createFileIfItDoesntExist": {"enabled": false, "createWithTemplate": false, "template": ""}, "format": {"enabled": true, "format": "```ad-col2\ntitle: {{value:请输入标题}}\ncolor:178,22,164\n{{VALUE:请输入内容1}}\n{{VALUE:请输入内容2}}\n```"}, "insertAfter": {"enabled": false, "after": "", "insertAtEnd": false, "considerSubsections": false, "createIfNotFound": false, "createIfNotFoundLocation": "top"}, "prepend": false, "task": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": false, "openFileInMode": "default"}, {"id": "94779998-5d39-4aeb-837a-3375bc815ef9", "name": "快速制作单词卡", "type": "Capture", "command": true, "appendLink": false, "captureTo": "", "captureToActiveFile": true, "createFileIfItDoesntExist": {"enabled": false, "createWithTemplate": false, "template": ""}, "format": {"enabled": true, "format": "### {{VALUE:请输入单词的英文}}\n?\n```ad-hibox\n{{VALUE:输入单词的意思}}\n```\nend"}, "insertAfter": {"enabled": false, "after": "", "insertAtEnd": false, "considerSubsections": false, "createIfNotFound": false, "createIfNotFoundLocation": "top"}, "prepend": false, "task": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": false, "openFileInMode": "default"}, {"id": "dbb204ea-2ed0-4e0f-a4d6-bf7744278de1", "name": "Spaced Repetition", "type": "Capture", "command": true, "appendLink": false, "captureTo": "", "captureToActiveFile": true, "createFileIfItDoesntExist": {"enabled": false, "createWithTemplate": false, "template": ""}, "format": {"enabled": true, "format": "## {{VALUE:请输入问题}}\n?\n```ad-success\ntitle: 答案\ncollapse: close\n {{VALUE:请输入答案}}\n```\nend"}, "insertAfter": {"enabled": false, "after": "", "insertAtEnd": false, "considerSubsections": false, "createIfNotFound": false, "createIfNotFoundLocation": "top"}, "prepend": false, "task": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": false, "openFileInMode": "default"}, {"id": "f590b6af-2cdf-4aff-ad44-965957eee3dc", "name": "anki模板", "type": "Capture", "command": true, "appendLink": false, "captureTo": "", "captureToActiveFile": true, "createFileIfItDoesntExist": {"enabled": false, "createWithTemplate": false, "template": ""}, "format": {"enabled": true, "format": "### 问题{{VALUE:这是问题几呢}}\n#### {{VALUE:描述一下这个问题吧}}\n{{VALUE:这里可以输入选项或者代码，建议先回车跳过自行输入}}\n?\n#### 正确答案\n{{VALUE:描述一下正确答案吧}}\n\nend"}, "insertAfter": {"enabled": false, "after": "", "insertAtEnd": false, "considerSubsections": false, "createIfNotFound": false, "createIfNotFoundLocation": "top"}, "prepend": false, "task": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": false, "openFileInMode": "default"}], "collapsed": false}, {"id": "3f7d8324-5fa3-4630-9afb-3b129a1595d0", "name": "为当前笔记创建Excalidraw", "type": "Macro", "command": true, "macroId": "110ff643-6e48-4a8a-b9ed-1f64354cd894"}], "macros": [{"name": "重新加载当前页", "id": "85aa978c-bf79-479c-98aa-70fc6776e303", "commands": [{"name": "关闭当前标签页", "type": "Obsidian", "id": "c9185ce0-940b-49ba-81e2-863f497ed92b", "commandId": "workspace:close"}, {"name": "Wait", "type": "Wait", "id": "0dab6894-fafa-4366-b659-5128abbcab87", "time": 100}, {"name": "重新打开标签页", "type": "Obsidian", "id": "a304e59b-660c-4b0f-ac87-b2b4c2aba266", "commandId": "workspace:undo-close-pane"}], "runOnStartup": false}, {"name": "切换阅读、预览、源码模式", "id": "77487bd7-1fc7-4389-ae8e-a7a0f11e7663", "commands": [{"name": "switch-view-mode", "type": "UserScript", "id": "6f398102-4a75-4b69-9d55-fdfd4373a550", "path": "att/QuickAdd/scripts/switch-view-mode.js", "settings": {}}], "runOnStartup": false}, {"name": "为当前笔记创建Excalidraw", "id": "110ff643-6e48-4a8a-b9ed-1f64354cd894", "commands": [{"name": "创建Excalidraw绘图", "type": "UserScript", "id": "create-excalidraw-script", "path": "creatExcalidraw.js", "settings": {}}], "runOnStartup": false}], "inputPrompt": "single-line", "devMode": false, "templateFolderPath": "", "announceUpdates": true, "version": "1.8.1", "disableOnlineFeatures": true, "ai": {"defaultModel": "Ask me", "defaultSystemPrompt": "As an AI assistant within Obsidian, your primary goal is to help users manage their ideas and knowledge more effectively. Format your responses using Markdown syntax. Please use the [[Obsidian]] link format. You can write aliases for the links by writing [[Obsidian|the alias after the pipe symbol]]. To use mathematical notation, use LaTeX syntax. LaTeX syntax for larger equations should be on separate lines, surrounded with double dollar signs ($$). You can also inline math expressions by wrapping it in $ symbols. For example, use $$w_{ij}^{\text{new}}:=w_{ij}^{\text{current}}+etacdotdelta_jcdot x_{ij}$$ on a separate line, but you can write \"($eta$ = learning rate, $delta_j$ = error term, $x_{ij}$ = input)\" inline.", "promptTemplatesFolderPath": "", "showAssistant": true, "providers": [{"name": "OpenAI", "endpoint": "https://api.openai.com/v1", "apiKey": "", "models": [{"name": "gpt-3.5-turbo", "maxTokens": 4096}, {"name": "gpt-3.5-turbo-16k", "maxTokens": 16384}, {"name": "gpt-3.5-turbo-1106", "maxTokens": 16385}, {"name": "gpt-4", "maxTokens": 8192}, {"name": "gpt-4-32k", "maxTokens": 32768}, {"name": "gpt-4-1106-preview", "maxTokens": 128000}, {"name": "text-davinci-003", "maxTokens": 4096}]}]}, "migrations": {"migrateToMacroIDFromEmbeddedMacro": true, "useQuickAddTemplateFolder": true, "incrementFileNameSettingMoveToDefaultBehavior": true, "mutualExclusionInsertAfterAndWriteToBottomOfFile": true, "setVersionAfterUpdateModalRelease": true, "addDefaultAIProviders": true}}