{"recentFiles": [{"basename": "3. 反向传播（Back Propagation）", "path": "学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md"}, {"basename": "8. 多分类问题 (Softmax Classifier)", "path": "学习库/Deep learning/pytorch/8. 多分类问题 (Softmax Classifier).md"}, {"basename": "Drawing 2025-04-27 16.50.59.excalidraw", "path": "学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md"}, {"basename": "Home", "path": "Home/Home.md"}, {"basename": "Drawing 2025-07-31 17.48.31.excalidraw", "path": "学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md"}, {"basename": "1. 线性模型（Linear Model）", "path": "学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md"}, {"basename": "未命名", "path": "学习库/An<PERSON>/Artificial Intelligence/未命名.md"}, {"basename": "评价指标", "path": "学习库/Deep learning/概念库/评价指标.md"}, {"basename": "2. 梯度下降算法（Gradient Descent Algorithm）", "path": "学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md"}, {"basename": "4. 使用pytorch实现线性模型", "path": "学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md"}, {"basename": "SCI", "path": "工作库/项目/舌诊/SCI.md"}, {"basename": "mcp", "path": "学习库/Artificial Intelligence/mcp.md"}, {"basename": "未命名 1", "path": "学习库/An<PERSON>/Artificial Intelligence/未命名 1.md"}, {"basename": "Drawing 2025-07-26 21.13.14.excalidraw", "path": "学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md"}, {"basename": "5. 逻辑回归（Logisitic Regression)", "path": "学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md"}, {"basename": "人脸识别", "path": "工作库/项目/舌诊/人脸识别.md"}, {"basename": "wsl2", "path": "学习库/linux/wsl2.md"}, {"basename": "2 核心语法", "path": "学习库/c/2 核心语法.md"}, {"basename": "1 从这开始吧", "path": "学习库/c/1 从这开始吧.md"}, {"basename": "0 一些操作", "path": "学习库/c/0 一些操作.md"}, {"basename": "4 流程控制语句", "path": "学习库/c/4 流程控制语句.md"}, {"basename": "未命名", "path": "折腾库/未命名.md"}, {"basename": "Prompt, Agent, MCP", "path": "学习库/Artificial Intelligence/Prompt, Agent, MCP.md"}, {"basename": "未命名", "path": "学习库/Docker/未命名.md"}, {"basename": "文献检索指南", "path": "学习库/心得/文献检索指南.md"}, {"basename": "缝合笔记", "path": "学习库/心得/缝合笔记.md"}, {"basename": "大队长手把手带你发论文_V2_工房版", "path": "学习库/心得/大队长手把手带你发论文_V2_工房版.pdf"}, {"basename": "Latex 从入门到如土", "path": "学习库/Latex/Latex 从入门到如土.md"}, {"basename": "未命名", "path": "学习库/软件使用/vscode/未命名.md"}, {"basename": "使用手册", "path": "学习库/软件使用/Total commander/使用手册.md"}, {"basename": "未命名", "path": "学习库/<PERSON><PERSON>/机器人学/未命名.md"}, {"basename": "1 启动", "path": "学习库/stm32/1 启动.md"}, {"basename": "python", "path": "学习库/An<PERSON>/单词/python.md"}, {"basename": "tensorRT", "path": "工作库/项目/舌诊/tensorRT.md"}, {"basename": "6 中断", "path": "学习库/stm32/6 中断.md"}, {"basename": "未命名看板", "path": "Home/未命名看板.md"}, {"basename": "4 I2C", "path": "学习库/stm32/4 I2C.md"}, {"basename": "3 串口", "path": "学习库/stm32/3 串口.md"}, {"basename": "Drawing 2025-07-19 11.20.54.excalidraw", "path": "Excalidraw/Drawing 2025-07-19 11.20.54.excalidraw.md"}, {"basename": "Drawing 2025-07-19 08.28.31.excalidraw", "path": "Excalidraw/Drawing 2025-07-19 08.28.31.excalidraw.md"}, {"basename": "未命名", "path": "工作库/项目/论文撰写/未命名.md"}, {"basename": "2025-07-19", "path": "日记库/fleeting_notes/2025-07-19.md"}, {"basename": "Efficient KAN", "path": "工作库/项目/舌诊/Efficient KAN.md"}, {"basename": "线程执行", "path": "工作库/项目/舌诊/线程执行.md"}, {"basename": "未命名", "path": "工作库/项目/舌诊/未命名.md"}, {"basename": "图像格式", "path": "工作库/项目/舌诊/图像格式.md"}, {"basename": "交叉验证", "path": "学习库/Deep learning/概念库/交叉验证.md"}, {"basename": "机械臂", "path": "工作库/项目/舌诊/机械臂.md"}, {"basename": "2025-07-18", "path": "日记库/fleeting_notes/2025-07-18.md"}, {"basename": "2 GPIO", "path": "学习库/stm32/2 GPIO.md"}], "omittedPaths": [], "omittedTags": [], "updateOn": "file-open", "omitBookmarks": false}