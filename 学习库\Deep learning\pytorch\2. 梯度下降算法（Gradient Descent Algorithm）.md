---
tags:
  - 学习
  - deep_learning
  - pytorch
---
# 2. 梯度下降算法（Gradient Descent Algorithm）

现实中的一切几乎都可以用函数来表示，就比如前面讲的 [[1. 线性模型（Linear Model）#问题引入|线性函数]] 的例子中，学生花了多少时间就能考多少分。我们想要找到这个函数关系，就是要让我们猜想的函数得到的预测值（$\hat{y}$）完全拟合真实的数据（$y$），所以我们需要将损失（预测值与真实值之间的差）降至尽可能的低，如下所示：
$$
loss = (\hat{y}-y)^2 = (\omega \times x - y)^2
$$
要想求这个函数的最小值，可以求其导函数，找到导数值为 0 的点，这个点就**有可能是最小值**，但注意这里的变量是 $\omega$（因为拟合就是在不断调整斜率也就是 $\omega$），所以是要对 $\omega$ 进行求导
$$
\frac{\partial loss}{\partial \omega} = 
\frac{\partial (\omega \times x - y)^2}{\partial \omega} 
$$
这对于简单的线性模型很有效，但是如果是==复杂的非线性模型==，是找不到这么完美的权重的，所以就要像下山一样一步一步的往下走（迭代更新权重），使得权重走到山底（最小的损失值），这就是==梯度下降==。 

![[2. 梯度下降算法（Gradient Descent Algorithm）-2025-07-31-21-15-34.png]]

- **梯度下降的目的**：
  - 通过最小化 [[1. 线性模型（Linear Model）#损失函数 Loss Function 与误差函数 Cost Function|损失函数]] 来找到最佳的模型参数（权重和偏置）
  - 通过==迭代更新模型==参数来逐步接近最优解

- **简单搜索算法的局限**：
  - 简单搜索算法是通过随机选择参数来寻找最优解，这种方法效率低下，加入一个权重可以取100个值，那么两个权重就有$100^2=(10000)$种组合，对于拥有百位参数的深度神经网络，几乎不可能找到最优解。

---

## 梯度下降的基本原理

````ad-info
title: 凸函数与非凸函数
collapse:close
- **凸函数**：凸函数是指在函数曲线上（或者高维空间的曲面上），任意两点之间的连线都在函数曲线（或者曲面）上方，或者说函数的二阶导数大于等于0。

- **非突函数与优化挑战**：
	- **局部最小值(Local Minimum)**：在某个区域内的最小值，其函数值低于所有邻近点的函数值，但不是全局最小值。
	- **全局最小值(Global Minimum)**：在整个函数域内的最小值。 
	- **鞍点(Saddle Point)**：在某个方向上是局部最小值，在另一个方向上是局部最大值，就像马鞍一样，在一个方向（前后）是向下弯曲的，但在垂直方向（左右）是向上弯曲的。 

![[2. 梯度下降算法.（Gradient Descent Algorithm）-2025-04-27-16-59-13.png]]
````

首先是计算当前权重所在位置的梯度（gradient），也就是损失函数（cost）对权重（$\omega$）进行求导，计算当前的导数值

![[2. 梯度下降算法.（Gradient Descent Algorithm）-2025-04-27-22-01-56.png|300]]
```ad-info
title: 导函数的方向与损失函数的关系
collapse: close
- 正的导函数（斜率为正）的导数值沿着坐标轴的正方向上增加
- 负的导函数（斜率为负）的导数值沿坐标轴的**负方向**增加
对于下面这张图，导函数为正，所以权重$\omega$越大，那么损失就会越大，这不是我们所希望的，所以我们就要让$\omega$变小（沿这坐标轴的负方向走）

![[2. 梯度下降算法.（Gradient Descent Algorithm）-2025-04-27-22-02-56.png|300]]

```

然后为了让 cost 函数的值更小，应该让权重（$\omega$）沿着梯度的反方向走 ^bwp6bp 

![[2. 梯度下降算法.（Gradient Descent Algorithm）-2025-04-27-22-17-12.png|300]]

因此新的权重($\omega_1$)可以表示为：
$$\omega_{1} = \omega - \alpha \frac{\partial cost}{\partial \omega}$$
-  $\alpha$ (学习率，Learning Rate)：这是一个正值，==决定了沿着负梯度方向前进的步长大小==，如果学习率太大，那么新的权重就有可能越过最小值而导致损失无法收敛。
-   $\alpha\frac{\partial cost}{\partial \omega}$ (梯度，Gradient)：损失函数对权重的导数（这里不考虑偏置 $b$，如果考虑就是偏导）


## 梯度下降的局限性以及解决方法

梯度下降是一种**贪心算法（Greedy Algorithm）**，它只考虑当前点的梯度信息，而不考虑全局信息，一旦达到局部最小值，此时的梯度会为0，算法就会停止更新权重，导致无法找到全局最小值。
比如在深度学习中，使用梯度下降可能会陷入鞍点，此时梯度会为0，导致算法无法继续更新权重，从而无法找到全局最小值。
所以以下梯度下降算法的变体就是为了解决这个问题的：

```ad-example
title:批量梯度下降（Batch Grandient Descent, BGD）（也称为全量梯度下降）
批量梯度下降（Batch Gradient Descent, BGD）是梯度下降的一种变体，它在每次迭代中使用整个训练集来计算梯度。这样可以保证每次更新都是朝着全局最小值的方向前进，但计算量较大，尤其是在数据集较大时。
- 优点：梯度估计非常稳定，收敛过程平滑
- 缺点：每次迭代都使用整个训练集来计算梯度，计算量较大，尤其是在数据集较大时，可能会导致内存不足或计算时间过长
```

```ad-example
title: 随机梯度下降（Stochastic Gradient Descent, SGD
随机梯度下降（Stochastic Gradient Descent, SGD）是梯度下降的一种变体，它在==每次迭代中只使用一个样本来计算梯度==，而不是使用整个训练集。这样可以加快收敛速度，并且可以避免陷入局部最小值。

公式如下，其中 $\frac{\partial loss}{\partial \omega}$ 是当前样本的梯度，$\alpha$ 是学习率
$$
\omega = \omega - \alpha \frac{\partial loss}{\partial \omega}
$$

- 梯度：与梯度下降对整个 [[1. 线性模型（Linear Model）#损失函数 Loss Function 与误差函数 Cost Function|cost]] 进行求偏导计算梯度不同，这里的梯度是随机选取的一个样本的 [[1. 线性模型（Linear Model）#损失函数 Loss Function 与误差函数 Cost Function|loss函数]] 的偏导数。
- 优点：为梯度的估计提供了更多的随机性，避免了陷入局部最小值
- 缺点：一次处理一个样本会造成更新之间的依赖，计算$i+1$的梯度需要依赖$i$更新后的权重，这意味着这些步骤没法同时进行，导致收敛速度变慢
```

````ad-example
title: 小批量梯度下降（Mini-batch Gradient Descent）
小批量梯度下降（Mini-batch Gradient Descent）是梯度下降的另一种变体，它在==每次迭代中使用一个小批量的样本==来计算梯度，而不是使用整个训练集或一个样本。这样可以平衡计算效率和收敛速度。这是现代深度学习中最常用的梯度下降算法，往往已经被简单的称为"SGD"

- 与随机梯度下降的区别：
  - 随机梯度下降是每次迭代使用一个样本来计算梯度，而小批量梯度下降是每次迭代使用一个小批量的样本来计算梯度
  - 小批量梯度下降可以更好地利用向量化运算，提高计算效率

```ad-abstract
title: 举例
假设有10000个训练样本，模型有100个权重（$\omega_1, \omega_2, \omega_3......\omega_{100}$）
1. **初始化**：首先先对这100个权重进行随机初始化
2. **划分数据**：将这10000个样本进行分成若干个batch，每个batch的batchsize设置为32，那么可以得到10000/32=313个batch
3. **迭代更新**：
	1. **计算平均损失**：先抽取第一个batch，计算这个batch中的32个样本的[[1. 线性模型（Linear Model）#损失函数(Loss Function)与误差函数(Cost Function)|MSE]]（loss）
	2. **计算平均梯度**：然后根据这个平均损失计算每一个权重（$\omega_{i}$）的梯度。
	3. **更新权重**：然后对于每一个权重$\omega_i$，以此将当前的权重-学习率×平均梯度得到新的权重（$\omega_{i(new)}=\omega_{i(old)}-\alpha \cdot \frac{\partial loss}{\partial \omega}$）
	4. **重复**：取出下一个batch重复执行上述操作，直到313个batch都执行完
```
````

^ycsbf4



## Batch Size 示例

使用包含5000条客户评论的数据集
- `Batch Size = 1` （随机梯度下降，Stochastic Gradient Descent, SGD）：每次迭代使用1条评论来计算梯度，更新权重，这个过程会对所有 5,000 条评论依次进行，意味着在一个 epoch 内会进行 5,000 次独立的参数更新。
- `Batch Size = 100` （小批量梯度下降，Mini-batch Gradient Descent）： 模型将处理前 100 条评论，根据这 100 条评论计算集体误差，然后更新其参数。接着处理下 100 条评论，再次更新，以此类推，直到在一个 epoch 内处理完所有 5,000 条评论。
- `Batch Size = 5000` （批量梯度下降，Batch Gradient Descent）：模型处理所有 5,000 条评论，计算整个数据集上的集体误差，然后针对这整个 epoch 只更新一次参数。